#!/usr/bin/env python3
"""
Script to extract all serial numbers from IDlist.txt file.
This script will process the file and extract all serial numbers,
handling both single-line and multi-line formats.
"""

import re
import os

def extract_serial_numbers(input_file, output_file):
    """
    Extract all serial numbers from the input file and save to output file.
    
    Args:
        input_file (str): Path to the input file containing serial numbers
        output_file (str): Path to the output file to save extracted serial numbers
    """
    serial_numbers = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split content into lines
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines and section headers (lines ending with ':' or '：')
            if not line or line.endswith(':') or line.endswith('：'):
                continue
            
            # Remove trailing comma if present
            line = line.rstrip(',')
            
            # Check if line contains multiple serial numbers separated by commas
            if ',' in line:
                # Split by comma and process each part
                parts = line.split(',')
                for part in parts:
                    part = part.strip()
                    if part and is_valid_serial_number(part):
                        serial_numbers.append(part)
            else:
                # Single serial number on the line
                if is_valid_serial_number(line):
                    serial_numbers.append(line)
        
        # Remove duplicates while preserving order
        unique_serial_numbers = []
        seen = set()
        for serial in serial_numbers:
            if serial not in seen:
                unique_serial_numbers.append(serial)
                seen.add(serial)
        
        # Write to output file
        with open(output_file, 'w', encoding='utf-8') as f:
            for serial in unique_serial_numbers:
                f.write(serial + '\n')
        
        print(f"Successfully extracted {len(unique_serial_numbers)} unique serial numbers")
        print(f"Total serial numbers found (including duplicates): {len(serial_numbers)}")
        print(f"Serial numbers saved to: {output_file}")
        
        return unique_serial_numbers
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
        return []
    except Exception as e:
        print(f"Error processing file: {e}")
        return []

def is_valid_serial_number(text):
    """
    Check if the text looks like a valid serial number.
    Serial numbers typically contain alphanumeric characters.
    
    Args:
        text (str): Text to validate
        
    Returns:
        bool: True if text looks like a valid serial number
    """
    if not text:
        return False
    
    # Remove whitespace
    text = text.strip()
    
    # Should be at least 4 characters long and contain only alphanumeric characters
    if len(text) < 4:
        return False
    
    # Check if it contains only alphanumeric characters (letters and numbers)
    return re.match(r'^[A-Za-z0-9]+$', text) is not None

def main():
    """Main function to run the serial number extraction."""
    input_file = 'IDlist.txt'
    output_file = 'extracted_serial_numbers.txt'
    
    print("Starting serial number extraction...")
    print(f"Input file: {input_file}")
    print(f"Output file: {output_file}")
    print("-" * 50)
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found in current directory")
        return
    
    # Extract serial numbers
    serial_numbers = extract_serial_numbers(input_file, output_file)
    
    if serial_numbers:
        print("-" * 50)
        print("First 10 extracted serial numbers:")
        for i, serial in enumerate(serial_numbers[:10], 1):
            print(f"{i:2d}. {serial}")
        
        if len(serial_numbers) > 10:
            print(f"... and {len(serial_numbers) - 10} more")

if __name__ == "__main__":
    main()
