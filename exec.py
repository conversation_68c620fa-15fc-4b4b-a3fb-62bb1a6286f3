import requests
import json

base_url = "https://nlb-lrwcurgcsatuhvfiz3.cn-shanghai.nlb.aliyuncsslb.com/api/device/%s/remotecommand"

cmd1 = "rm -f /var/lib/dpkg/info/com.data.iantscore.plugin.python3.*"
cmd2 = "dpkg --configure -a"
cmd3 = "apt-get install -f -y"


payload = json.dumps({
   "command": "touch /tmp/123"
})
headers = {
   'Content-Type': 'application/json',
   'Authorization': 'Bearer data666iants.'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)