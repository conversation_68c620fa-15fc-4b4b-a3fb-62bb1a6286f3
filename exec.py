import requests
import json
import time

# 基础URL，%s会被序列号替换
base_url = "https://nlb-lrwcurgcsatuhvfiz3.cn-shanghai.nlb.aliyuncsslb.com/api/device/%s/remotecommand"

# 要执行的命令
cmd2 = "dpkg --configure -a"
cmd3 = "apt-get install -f -y"

# 请求头
headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer data666iants.'
}

def main():
    # 读取序列号文件
    with open('extracted_serial_numbers.txt', 'r') as f:
        serial_numbers = [line.strip() for line in f if line.strip()]

    print(f"开始处理 {len(serial_numbers)} 个设备...")

    # 遍历所有序列号
    for i, serial_number in enumerate(serial_numbers, 1):
        print(f"[{i}/{len(serial_numbers)}] 处理设备: {serial_number}")

        # 构建URL
        url = base_url % serial_number

        # 执行cmd2
        payload = json.dumps({"command": cmd2})
        try:
            response = requests.post(url, headers=headers, data=payload, timeout=10)
            print(f"  CMD2状态: {response.status_code}")
        except:
            print(f"  CMD2失败")

        time.sleep(0.5)

        # 执行cmd3
        payload = json.dumps({"command": cmd3})
        try:
            response = requests.post(url, headers=headers, data=payload, timeout=10)
            print(f"  CMD3状态: {response.status_code}")
        except:
            print(f"  CMD3失败")

        time.sleep(1)  # 设备间隔

    print("处理完成!")

if __name__ == "__main__":
    main()