import requests
import json
import time
import os

base_url = "https://nlb-lrwcurgcsatuhvfiz3.cn-shanghai.nlb.aliyuncsslb.com/api/device/%s/remotecommand"

# Define the three commands to execute
cmd1 = "rm -f /var/lib/dpkg/info/com.data.iantscore.plugin.python3.*"
cmd2 = "dpkg --configure -a"
cmd3 = "apt-get install -f -y"

# HTTP headers for requests
headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer data666iants.'
}

def read_serial_numbers(filename):
    """
    Read serial numbers from the extracted file.

    Args:
        filename (str): Path to the file containing serial numbers

    Returns:
        list: List of serial numbers
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            serial_numbers = [line.strip() for line in f if line.strip()]
        print(f"Successfully loaded {len(serial_numbers)} serial numbers from {filename}")
        return serial_numbers
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found")
        return []
    except Exception as e:
        print(f"Error reading file '{filename}': {e}")
        return []

def send_command(serial_number, command, command_name):
    """
    Send a command to a specific device.

    Args:
        serial_number (str): Device serial number
        command (str): Command to execute
        command_name (str): Name of the command for logging

    Returns:
        bool: True if request was successful, False otherwise
    """
    try:
        # Format the URL with the serial number
        url = base_url % serial_number

        # Create payload with the command
        payload = json.dumps({
            "command": command
        })

        # Send POST request
        response = requests.post(url, headers=headers, data=payload, timeout=30)

        print(f"  {command_name}: Status {response.status_code}")

        # Return True if status code indicates success (2xx)
        return 200 <= response.status_code < 300

    except requests.exceptions.Timeout:
        print(f"  {command_name}: Request timeout")
        return False
    except requests.exceptions.RequestException as e:
        print(f"  {command_name}: Request failed - {e}")
        return False
    except Exception as e:
        print(f"  {command_name}: Unexpected error - {e}")
        return False

def process_device(serial_number, commands):
    """
    Process a single device by sending all commands.

    Args:
        serial_number (str): Device serial number
        commands (list): List of (command, command_name) tuples

    Returns:
        dict: Results summary for the device
    """
    print(f"\nProcessing device: {serial_number}")

    results = {
        'serial_number': serial_number,
        'commands_sent': 0,
        'commands_successful': 0,
        'commands_failed': 0
    }

    for command, command_name in commands:
        success = send_command(serial_number, command, command_name)
        results['commands_sent'] += 1

        if success:
            results['commands_successful'] += 1
        else:
            results['commands_failed'] += 1

        # Small delay between commands to avoid overwhelming the server
        time.sleep(0.5)

    return results

def main():
    """Main function to execute commands on all devices."""
    print("Starting device command execution...")
    print("=" * 60)

    # Read serial numbers from file
    serial_numbers_file = 'extracted_serial_numbers.txt'
    serial_numbers = read_serial_numbers(serial_numbers_file)

    if not serial_numbers:
        print("No serial numbers found. Exiting.")
        return

    # Define commands to execute
    commands = [
        (cmd1, "CMD1 (rm dpkg info)"),
        (cmd2, "CMD2 (dpkg configure)"),
        (cmd3, "CMD3 (apt-get install)")
    ]

    # Process all devices
    total_devices = len(serial_numbers)
    processed_devices = 0
    successful_devices = 0
    failed_devices = 0

    print(f"\nStarting to process {total_devices} devices...")
    print("-" * 60)

    for i, serial_number in enumerate(serial_numbers, 1):
        print(f"\n[{i}/{total_devices}] Processing device {serial_number}")

        try:
            results = process_device(serial_number, commands)
            processed_devices += 1

            if results['commands_failed'] == 0:
                successful_devices += 1
                print(f"  ✓ All commands successful for {serial_number}")
            else:
                failed_devices += 1
                print(f"  ⚠ {results['commands_failed']}/{results['commands_sent']} commands failed for {serial_number}")

        except KeyboardInterrupt:
            print(f"\n\nOperation interrupted by user at device {i}/{total_devices}")
            break
        except Exception as e:
            print(f"  ✗ Unexpected error processing {serial_number}: {e}")
            failed_devices += 1
            processed_devices += 1

        # Small delay between devices
        if i < total_devices:
            time.sleep(1)

    # Print final summary
    print("\n" + "=" * 60)
    print("EXECUTION SUMMARY")
    print("=" * 60)
    print(f"Total devices: {total_devices}")
    print(f"Processed devices: {processed_devices}")
    print(f"Successful devices: {successful_devices}")
    print(f"Failed devices: {failed_devices}")
    print(f"Remaining devices: {total_devices - processed_devices}")

    if processed_devices > 0:
        success_rate = (successful_devices / processed_devices) * 100
        print(f"Success rate: {success_rate:.1f}%")

if __name__ == "__main__":
    main()